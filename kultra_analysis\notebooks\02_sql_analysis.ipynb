{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Kultra Mega Stores - SQL Business Intelligence Analysis\n", "## DSA Capstone Project - Case Study 2\n", "\n", "### Objective\n", "Perform comprehensive SQL-based analysis to answer business questions for Kultra Mega Stores (KMS) Abuja division.\n", "\n", "### Business Context\n", "- **Company**: Kultra Mega Stores (KMS)\n", "- **Industry**: Office supplies and furniture\n", "- **Analysis Period**: 2009-2012\n", "- **Customer Segments**: Individual consumers, small businesses, corporate clients\n", "\n", "### Analysis Scenarios\n", "**Scenario I**: Sales & Operations Analysis (Questions 1-5)  \n", "**Scenario II**: Customer & Profitability Analysis (Questions 6-11)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sqlite3\n", "from sqlalchemy import create_engine\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_rows', 100)\n", "pd.set_option('display.float_format', '{:.2f}'.format)\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"Set2\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Database Setup and Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create SQLite database and load data\n", "try:\n", "    # Load Excel data\n", "    df = pd.read_excel('../data/raw/kms_orders.xlsx')  # Update with actual filename\n", "    print(f\"Data loaded successfully: {df.shape}\")\n", "    print(f\"Columns: {list(df.columns)}\")\n", "    \n", "    # Create SQLite database\n", "    engine = create_engine('sqlite:///../data/processed/kms_database.db')\n", "    \n", "    # Load data into database\n", "    df.to_sql('orders', engine, if_exists='replace', index=False)\n", "    print(\"Data loaded into SQLite database successfully!\")\n", "    \n", "    # Display basic info\n", "    print(\"\\n=== DATASET OVERVIEW ===\")\n", "    print(df.info())\n", "    print(\"\\n=== FIRST FEW ROWS ===\")\n", "    display(df.head())\n", "    \nexcept FileNotFoundError:\n", "    print(\"Excel file not found. Please ensure the data file is in the correct location.\")\n", "    print(\"Expected location: ../data/raw/kms_orders.xlsx\")\nexcept Exception as e:\n", "    print(f\"Error loading data: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scenario I: Sales & Operations Analysis\n", "\n", "### Question 1: Which product category had the highest sales?"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SQL Query for Question 1\n", "query_1 = \"\"\"\n", "SELECT \n", "    category,\n", "    SUM(sales) as total_sales,\n", "    COUNT(*) as order_count,\n", "    AVG(sales) as avg_sales_per_order,\n", "    ROUND(SUM(sales) * 100.0 / (SELECT SUM(sales) FROM orders), 2) as sales_percentage\n", "FROM orders\n", "GROUP BY category\n", "ORDER BY total_sales DESC;\n", "\"\"\"\n", "\n", "if 'engine' in locals():\n", "    result_1 = pd.read_sql_query(query_1, engine)\n", "    \n", "    print(\"=== QUESTION 1: SALES BY PRODUCT CATEGORY ===\")\n", "    display(result_1)\n", "    \n", "    # Visualization\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))\n", "    \n", "    # Total sales by category\n", "    sns.barplot(data=result_1, x='total_sales', y='category', ax=ax1)\n", "    ax1.set_title('Total Sales by Product Category', fontsize=14, fontweight='bold')\n", "    ax1.set_xlabel('Total Sales ($)')\n", "    \n", "    # Sales percentage pie chart\n", "    ax2.pie(result_1['sales_percentage'], labels=result_1['category'], autopct='%1.1f%%', startangle=90)\n", "    ax2.set_title('Sales Distribution by Category', fontsize=14, fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(f\"\\n📊 KEY INSIGHT: {result_1.iloc[0]['category']} has the highest sales with ${result_1.iloc[0]['total_sales']:,.2f} ({result_1.iloc[0]['sales_percentage']}% of total sales)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question 2: Top 3 and Bottom 3 regions in terms of sales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SQL Query for Question 2\n", "query_2 = \"\"\"\n", "SELECT \n", "    region,\n", "    SUM(sales) as total_sales,\n", "    COUNT(*) as order_count,\n", "    AVG(sales) as avg_sales_per_order,\n", "    RANK() OVER (ORDER BY SUM(sales) DESC) as sales_rank\n", "FROM orders\n", "GROUP BY region\n", "ORDER BY total_sales DESC;\n", "\"\"\"\n", "\n", "if 'engine' in locals():\n", "    result_2 = pd.read_sql_query(query_2, engine)\n", "    \n", "    # Identify top 3 and bottom 3\n", "    top_3 = result_2.head(3).copy()\n", "    bottom_3 = result_2.tail(3).copy()\n", "    \n", "    top_3['performance'] = 'Top 3'\n", "    bottom_3['performance'] = 'Bottom 3'\n", "    \n", "    print(\"=== QUESTION 2: REGIONAL SALES PERFORMANCE ===\")\n", "    print(\"\\n🏆 TOP 3 REGIONS:\")\n", "    display(top_3[['region', 'total_sales', 'order_count', 'avg_sales_per_order']])\n", "    \n", "    print(\"\\n📉 BOTTOM 3 REGIONS:\")\n", "    display(bottom_3[['region', 'total_sales', 'order_count', 'avg_sales_per_order']])\n", "    \n", "    # Visualization\n", "    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))\n", "    \n", "    # All regions sales\n", "    sns.barplot(data=result_2, x='total_sales', y='region', ax=ax1)\n", "    ax1.set_title('Total Sales by Region (All Regions)', fontsize=14, fontweight='bold')\n", "    ax1.set_xlabel('Total Sales ($)')\n", "    \n", "    # Top 3 vs Bottom 3 comparison\n", "    comparison_data = pd.concat([top_3, bottom_3])\n", "    sns.barplot(data=comparison_data, x='total_sales', y='region', hue='performance', ax=ax2)\n", "    ax2.set_title('Top 3 vs Bottom 3 Regions Comparison', fontsize=14, fontweight='bold')\n", "    ax2.set_xlabel('Total Sales ($)')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(f\"\\n📊 KEY INSIGHTS:\")\n", "    print(f\"• Best performing region: {top_3.iloc[0]['region']} (${top_3.iloc[0]['total_sales']:,.2f})\")\n", "    print(f\"• Worst performing region: {bottom_3.iloc[-1]['region']} (${bottom_3.iloc[-1]['total_sales']:,.2f})\")\n", "    print(f\"• Performance gap: ${top_3.iloc[0]['total_sales'] - bottom_3.iloc[-1]['total_sales']:,.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question 3: Total sales of appliances in Ontario"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SQL Query for Question 3\n", "query_3 = \"\"\"\n", "SELECT \n", "    region,\n", "    category,\n", "    SUM(sales) as total_appliance_sales,\n", "    COUNT(*) as appliance_orders,\n", "    AVG(sales) as avg_appliance_sale,\n", "    MIN(sales) as min_sale,\n", "    MAX(sales) as max_sale\n", "FROM orders\n", "WHERE region = 'Ontario' \n", "    AND LOWER(category) LIKE '%appliance%'\n", "GROUP BY region, category;\n", "\"\"\"\n", "\n", "# Also get sub-category breakdown\n", "query_3_detailed = \"\"\"\n", "SELECT \n", "    region,\n", "    sub_category,\n", "    SUM(sales) as total_sales,\n", "    COUNT(*) as order_count,\n", "    AVG(sales) as avg_sale\n", "FROM orders\n", "WHERE region = 'Ontario' \n", "    AND LOWER(category) LIKE '%appliance%'\n", "GROUP BY region, sub_category\n", "ORDER BY total_sales DESC;\n", "\"\"\"\n", "\n", "if 'engine' in locals():\n", "    result_3 = pd.read_sql_query(query_3, engine)\n", "    result_3_detailed = pd.read_sql_query(query_3_detailed, engine)\n", "    \n", "    print(\"=== QUESTION 3: AP<PERSON><PERSON><PERSON><PERSON> SALES IN ONTARIO ===\")\n", "    \n", "    if len(result_3) > 0:\n", "        display(result_3)\n", "        \n", "        print(\"\\n=== APPLIANCE SUB-CATEGORIES IN ONTARIO ===\")\n", "        display(result_3_detailed)\n", "        \n", "        # Visualization\n", "        if len(result_3_detailed) > 0:\n", "            plt.figure(figsize=(14, 8))\n", "            sns.barplot(data=result_3_detailed, x='total_sales', y='sub_category')\n", "            plt.title('Appliance Sales by Sub-Category in Ontario', fontsize=14, fontweight='bold')\n", "            plt.xlabel('Total Sales ($)')\n", "            plt.tight_layout()\n", "            plt.show()\n", "            \n", "            total_appliance_sales = result_3_detailed['total_sales'].sum()\n", "            print(f\"\\n📊 KEY INSIGHT: Total appliance sales in Ontario: ${total_appliance_sales:,.2f}\")\n", "    else:\n", "        print(\"No appliance sales found in Ontario. Please check category naming conventions.\")\n", "        \n", "        # Let's check what categories exist in Ontario\n", "        ontario_categories = pd.read_sql_query(\"\"\"\n", "        SELECT category, COUNT(*) as count, SUM(sales) as total_sales\n", "        FROM orders \n", "        WHERE region = 'Ontario'\n", "        GROUP BY category\n", "        ORDER BY total_sales DESC\n", "        \"\"\", engine)\n", "        \n", "        print(\"\\n=== AVAIL<PERSON>LE CATEGORIES IN ONTARIO ===\")\n", "        display(ontario_categories)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question 4: Bottom 10 customers analysis for revenue optimization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SQL Query for Question 4\n", "query_4 = \"\"\"\n", "SELECT \n", "    customer_name,\n", "    customer_id,\n", "    segment,\n", "    SUM(sales) as total_sales,\n", "    COUNT(*) as order_count,\n", "    AVG(sales) as avg_order_value,\n", "    SUM(profit) as total_profit,\n", "    AVG(profit) as avg_profit_per_order,\n", "    MIN(order_date) as first_order,\n", "    MAX(order_date) as last_order\n", "FROM orders\n", "GROUP BY customer_name, customer_id, segment\n", "ORDER BY total_sales ASC\n", "LIMIT 10;\n", "\"\"\"\n", "\n", "# Analysis of bottom customers' purchasing patterns\n", "query_4_patterns = \"\"\"\n", "WITH bottom_customers AS (\n", "    SELECT customer_id\n", "    FROM orders\n", "    GROUP BY customer_id\n", "    ORDER BY SUM(sales) ASC\n", "    LIMIT 10\n", ")\n", "SELECT \n", "    o.customer_name,\n", "    o.segment,\n", "    o.category,\n", "    o.sub_category,\n", "    COUNT(*) as purchase_frequency,\n", "    SUM(o.sales) as category_sales,\n", "    AVG(o.sales) as avg_category_sale\n", "FROM orders o\n", "INNER JOIN bottom_customers bc ON o.customer_id = bc.customer_id\n", "GROUP BY o.customer_name, o.segment, o.category, o.sub_category\n", "ORDER BY o.customer_name, category_sales DESC;\n", "\"\"\"\n", "\n", "if 'engine' in locals():\n", "    result_4 = pd.read_sql_query(query_4, engine)\n", "    result_4_patterns = pd.read_sql_query(query_4_patterns, engine)\n", "    \n", "    print(\"=== QUESTION 4: BOTTOM 10 CUSTOMERS ANALYSIS ===\")\n", "    display(result_4)\n", "    \n", "    print(\"\\n=== BOTTOM 10 CUSTOMERS' PURCHASING PATTERNS ===\")\n", "    display(result_4_patterns.head(20))  # Show top 20 patterns\n", "    \n", "    # Visualization\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))\n", "    \n", "    # Bottom 10 customers sales\n", "    sns.barplot(data=result_4, x='total_sales', y='customer_name', ax=ax1)\n", "    ax1.set_title('Bottom 10 Customers by Total Sales', fontsize=12, fontweight='bold')\n", "    ax1.set_xlabel('Total Sales ($)')\n", "    \n", "    # Order frequency\n", "    sns.barplot(data=result_4, x='order_count', y='customer_name', ax=ax2)\n", "    ax2.set_title('Bottom 10 Customers by Order Count', fontsize=12, fontweight='bold')\n", "    ax2.set_xlabel('Number of Orders')\n", "    \n", "    # Segment distribution\n", "    segment_dist = result_4['segment'].value_counts()\n", "    ax3.pie(segment_dist.values, labels=segment_dist.index, autopct='%1.1f%%')\n", "    ax3.set_title('Bottom 10 Customers by Segment', fontsize=12, fontweight='bold')\n", "    \n", "    # Average order value\n", "    sns.barplot(data=result_4, x='avg_order_value', y='customer_name', ax=ax4)\n", "    ax4.set_title('Bottom 10 Customers by Avg Order Value', fontsize=12, fontweight='bold')\n", "    ax4.set_xlabel('Average Order Value ($)')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Revenue optimization recommendations\n", "    print(\"\\n💡 REVENUE OPTIMIZATION RECOMMENDATIONS:\")\n", "    avg_sales = result_4['total_sales'].mean()\n", "    avg_orders = result_4['order_count'].mean()\n", "    avg_order_value = result_4['avg_order_value'].mean()\n", "    \n", "    print(f\"• Average sales among bottom 10: ${avg_sales:.2f}\")\n", "    print(f\"• Average order count: {avg_orders:.1f}\")\n", "    print(f\"• Average order value: ${avg_order_value:.2f}\")\n", "    print(\"\\n🎯 STRATEGIES:\")\n", "    print(\"1. Implement customer loyalty programs\")\n", "    print(\"2. Offer volume discounts to increase order size\")\n", "    print(\"3. Cross-sell and upsell complementary products\")\n", "    print(\"4. Provide personalized product recommendations\")\n", "    print(\"5. Improve customer engagement through targeted marketing\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}