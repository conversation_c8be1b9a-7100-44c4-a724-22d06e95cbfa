# Amazon Product Review Analysis
## DSA Data Analysis Capstone Project - Case Study 1

### Project Overview
This repository contains a comprehensive Exploratory Data Analysis (EDA) of Amazon product review data for RetailTech Insights, an e-commerce analytics company. The analysis provides insights for product improvement, marketing strategies, and customer engagement.

### Business Context
**Company**: RetailTech Insights  
**Role**: Junior Data Analyst  
**Objective**: Analyze product and customer review data to generate actionable insights for Amazon sellers

### Dataset Description
- **Source**: Amazon product pages (scraped data)
- **Records**: 1,465 rows
- **Fields**: 16 columns
- **Content**: Product details, customer reviews, ratings, pricing information

### Key Analysis Questions
1. Average discount percentage by product category
2. Product distribution across categories
3. Total reviews per category
4. Highest-rated products identification
5. Price analysis (actual vs discounted) by category
6. Products with highest review counts
7. High-discount products (50%+ discount)
8. Rating distribution analysis
9. Revenue potential analysis
10. Price range segmentation
11. Discount-rating correlation
12. Low-review products identification
13. Category-wise discount leaders
14. Top products by combined rating and review metrics

### Deliverables
- **Data Cleaning & Preprocessing**: Python/Pandas scripts
- **Exploratory Data Analysis**: Jupyter notebooks with comprehensive analysis
- **Visualizations**: Charts and graphs using matplotlib, seaborn, plotly
- **Excel Dashboard**: Interactive dashboard with pivot tables and charts
- **Insights Report**: Business recommendations and key findings

### Tools & Technologies
- **Python**: pandas, numpy, matplotlib, seaborn, plotly
- **Excel**: Pivot tables, charts, dashboard creation
- **Jupyter Notebooks**: Analysis documentation
- **Git**: Version control

### Repository Structure
```
amazon_analysis/
├── data/
│   ├── raw/                    # Original dataset
│   ├── processed/              # Cleaned data
│   └── outputs/                # Analysis results
├── notebooks/
│   ├── 01_data_exploration.ipynb
│   ├── 02_data_cleaning.ipynb
│   ├── 03_eda_analysis.ipynb
│   └── 04_insights_visualization.ipynb
├── src/
│   ├── data_processing.py
│   ├── analysis_functions.py
│   └── visualization_utils.py
├── dashboards/
│   └── amazon_dashboard.xlsx
├── reports/
│   ├── analysis_report.md
│   └── executive_summary.md
├── requirements.txt
└── README.md
```

### Getting Started
1. Clone this repository
2. Install required packages: `pip install -r requirements.txt`
3. Run notebooks in sequence (01 → 02 → 03 → 04)
4. Open Excel dashboard for interactive exploration

### Key Findings
*[To be updated after analysis completion]*

### Business Recommendations
*[To be updated after analysis completion]*

### Author
DSA Data Analysis Capstone Project  
*[Your Name]*  
*[Date]*
