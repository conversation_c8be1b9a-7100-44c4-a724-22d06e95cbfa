-- Kultra Mega Stores (KMS) Business Intelligence Analysis
-- Case Scenario I: Sales & Operations Analysis
-- DSA Capstone Project - Case Study 2

-- =====================================================
-- SCENARIO I ANALYSIS QUERIES
-- =====================================================

-- Question 1: Which product category had the highest sales?
-- =====================================================
SELECT 
    category,
    SUM(sales) as total_sales,
    COUNT(*) as order_count,
    AVG(sales) as avg_sales_per_order,
    ROUND(SUM(sales) / (SELECT SUM(sales) FROM orders) * 100, 2) as sales_percentage
FROM orders
GROUP BY category
ORDER BY total_sales DESC;

-- Additional analysis: Monthly sales trend by category
SELECT 
    category,
    YEAR(order_date) as year,
    MONTH(order_date) as month,
    SUM(sales) as monthly_sales
FROM orders
GROUP BY category, YEAR(order_date), MONTH(order_date)
ORDER BY category, year, month;


-- Question 2: What are the Top 3 and Bottom 3 regions in terms of sales?
-- =====================================================

-- Top 3 regions by sales
SELECT 
    region,
    SUM(sales) as total_sales,
    COUNT(*) as order_count,
    AVG(sales) as avg_sales_per_order,
    'Top 3' as performance_category
FROM orders
GROUP BY region
ORDER BY total_sales DESC
LIMIT 3;

-- Bottom 3 regions by sales
SELECT 
    region,
    SUM(sales) as total_sales,
    COUNT(*) as order_count,
    AVG(sales) as avg_sales_per_order,
    'Bottom 3' as performance_category
FROM orders
GROUP BY region
ORDER BY total_sales ASC
LIMIT 3;

-- Combined view of all regions ranked
SELECT 
    region,
    SUM(sales) as total_sales,
    COUNT(*) as order_count,
    AVG(sales) as avg_sales_per_order,
    RANK() OVER (ORDER BY SUM(sales) DESC) as sales_rank,
    CASE 
        WHEN RANK() OVER (ORDER BY SUM(sales) DESC) <= 3 THEN 'Top 3'
        WHEN RANK() OVER (ORDER BY SUM(sales) DESC) >= (SELECT COUNT(DISTINCT region) FROM orders) - 2 THEN 'Bottom 3'
        ELSE 'Middle'
    END as performance_category
FROM orders
GROUP BY region
ORDER BY total_sales DESC;


-- Question 3: What were the total sales of appliances in Ontario?
-- =====================================================
SELECT 
    region,
    category,
    SUM(sales) as total_appliance_sales,
    COUNT(*) as appliance_orders,
    AVG(sales) as avg_appliance_sale,
    MIN(sales) as min_sale,
    MAX(sales) as max_sale
FROM orders
WHERE region = 'Ontario' 
    AND LOWER(category) LIKE '%appliance%'
GROUP BY region, category;

-- Additional: Appliance sales breakdown by sub-category in Ontario
SELECT 
    region,
    sub_category,
    SUM(sales) as total_sales,
    COUNT(*) as order_count
FROM orders
WHERE region = 'Ontario' 
    AND LOWER(category) LIKE '%appliance%'
GROUP BY region, sub_category
ORDER BY total_sales DESC;


-- Question 4: Bottom 10 customers analysis for revenue optimization
-- =====================================================
SELECT 
    customer_name,
    customer_id,
    segment,
    SUM(sales) as total_sales,
    COUNT(*) as order_count,
    AVG(sales) as avg_order_value,
    SUM(profit) as total_profit,
    AVG(profit) as avg_profit_per_order,
    MIN(order_date) as first_order,
    MAX(order_date) as last_order,
    DATEDIFF(MAX(order_date), MIN(order_date)) as customer_lifespan_days
FROM orders
GROUP BY customer_name, customer_id, segment
ORDER BY total_sales ASC
LIMIT 10;

-- Analysis of bottom 10 customers' purchasing patterns
WITH bottom_customers AS (
    SELECT customer_id
    FROM orders
    GROUP BY customer_id
    ORDER BY SUM(sales) ASC
    LIMIT 10
)
SELECT 
    o.customer_name,
    o.segment,
    o.category,
    o.sub_category,
    COUNT(*) as purchase_frequency,
    SUM(o.sales) as category_sales,
    AVG(o.sales) as avg_category_sale
FROM orders o
INNER JOIN bottom_customers bc ON o.customer_id = bc.customer_id
GROUP BY o.customer_name, o.segment, o.category, o.sub_category
ORDER BY o.customer_name, category_sales DESC;


-- Question 5: Shipping method with highest shipping cost
-- =====================================================
SELECT 
    ship_mode,
    SUM(shipping_cost) as total_shipping_cost,
    COUNT(*) as shipment_count,
    AVG(shipping_cost) as avg_shipping_cost,
    MIN(shipping_cost) as min_shipping_cost,
    MAX(shipping_cost) as max_shipping_cost,
    ROUND(SUM(shipping_cost) / (SELECT SUM(shipping_cost) FROM orders) * 100, 2) as cost_percentage
FROM orders
GROUP BY ship_mode
ORDER BY total_shipping_cost DESC;

-- Shipping cost analysis by order priority
SELECT 
    ship_mode,
    order_priority,
    SUM(shipping_cost) as total_shipping_cost,
    COUNT(*) as shipment_count,
    AVG(shipping_cost) as avg_shipping_cost
FROM orders
GROUP BY ship_mode, order_priority
ORDER BY ship_mode, total_shipping_cost DESC;

-- Shipping efficiency analysis (cost vs delivery time)
SELECT 
    ship_mode,
    AVG(shipping_cost) as avg_shipping_cost,
    AVG(DATEDIFF(ship_date, order_date)) as avg_delivery_days,
    COUNT(*) as total_shipments,
    SUM(sales) as total_sales_shipped
FROM orders
WHERE ship_date IS NOT NULL AND order_date IS NOT NULL
GROUP BY ship_mode
ORDER BY avg_shipping_cost DESC;


-- =====================================================
-- SUMMARY ANALYSIS FOR SCENARIO I
-- =====================================================

-- Executive summary query combining key metrics
SELECT 
    'Total Sales' as metric,
    CONCAT('$', FORMAT(SUM(sales), 2)) as value,
    '' as additional_info
FROM orders

UNION ALL

SELECT 
    'Total Orders' as metric,
    FORMAT(COUNT(*), 0) as value,
    '' as additional_info
FROM orders

UNION ALL

SELECT 
    'Average Order Value' as metric,
    CONCAT('$', FORMAT(AVG(sales), 2)) as value,
    '' as additional_info
FROM orders

UNION ALL

SELECT 
    'Top Category' as metric,
    category as value,
    CONCAT('$', FORMAT(SUM(sales), 2), ' in sales') as additional_info
FROM orders
GROUP BY category
ORDER BY SUM(sales) DESC
LIMIT 1;
