Amazon Product Review Analysis - Complete Excel Implementation Guide
DSA Capstone Project - Case Study 1

OVERVIEW
This guide provides step-by-step instructions to analyze the Amazon dataset using Excel to answer all 14 business questions with formulas, pivot tables, and visualizations.

EXCEL WORKBOOK STRUCTURE

Sheet 1: Raw Data
- Import the Amazon case study.xlsx data
- Ensure all columns are properly formatted
- Add calculated columns as needed

Sheet 2: Data Preparation
- Clean and prepare data for analysis
- Create calculated columns
- Set up data validation

Sheet 3-16: Analysis Questions (Q1-Q14)
- One sheet per business question
- Include pivot tables, formulas, and charts
- Provide insights and interpretations

Sheet 17: Executive Dashboard
- Summary of all key findings
- Interactive charts and KPIs
- Business recommendations

STEP-BY-STEP IMPLEMENTATION

Step 1: Data Preparation Sheet

A. Create Calculated Columns
Add these columns to your data (assuming columns A-P contain original data):

Column Q: Discount_Percentage
=IF(R2>0, (R2-S2)/R2*100, 0)
Where R=actual_price, S=discounted_price

Column R: Price_Range
=IF(S2<200,"<₹200",IF(S2<=500,"₹200-₹500",">₹500"))
Where S=discounted_price

Column S: Rating_Category
=IF(H2>=4.5,"Excellent",IF(H2>=4,"Very Good",IF(H2>=3.5,"Good",IF(H2>=3,"Average","Below Average"))))
Where H=rating

Column T: Review_Category
=IF(I2>=1000,"High Engagement",IF(I2>=100,"Medium Engagement","Low Engagement"))
Where I=rating_count

Column U: Revenue_Potential
=R2*I2
actual_price * rating_count

ANALYSIS QUESTIONS IMPLEMENTATION

Question 1: Average Discount Percentage by Category

Sheet: Q1_Discount_Analysis

Pivot Table Setup:
- Rows: Category
- Values: Average of Discount_Percentage, Count of Product_Name
- Add calculated field for weighted average

Key Formulas:
In summary table
=AVERAGEIF(Data!$B:$B,A2,Data!$Q:$Q)  Average discount by category
=COUNTIF(Data!$B:$B,A2)                Product count by category

Chart: Horizontal bar chart showing average discount by category

Analysis Questions to Answer:
- Which category offers the highest average discount?
- What's the range of discounts across categories?
- How does product count relate to discount levels?

Question 2: Product Distribution Across Categories

Sheet: Q2_Product_Distribution

Pivot Table Setup:
- Rows: Category
- Values: Count of Product_Name
- Add percentage of total

Key Formulas:
=COUNTIF(Data!$B:$B,A2)                    Count by category
=COUNTIF(Data!$B:$B,A2)/COUNTA(Data!$B:$B)*100  Percentage

Charts:
- Pie chart for distribution
- Bar chart for counts

Question 3: Total Reviews per Category

Sheet: Q3_Reviews_Analysis

Pivot Table Setup:
- Rows: Category
- Values: Sum of Rating_Count, Average of Rating_Count, Count of Product_Name

Key Formulas:
=SUMIF(Data!$B:$B,A2,Data!$I:$I)      Total reviews by category
=AVERAGEIF(Data!$B:$B,A2,Data!$I:$I)  Average reviews per product

Chart: Combination chart (columns for total, line for average)

Question 4: Highest-Rated Products

Sheet: Q4_Top_Rated_Products

Analysis Setup:
Filter for products with >100 reviews first
=FILTER(Data!A:U,(Data!I:I>=100)*(Data!H:H>=4.5))

Top 20 highest-rated products
=LARGE(Data!$H:$H,ROW(A1:A20))  Get top 20 ratings
=INDEX(Data!$A:$A,MATCH(B2,Data!$H:$H,0))  Get product names

Chart: Scatter plot (Rating vs Review Count)

Question 5: Price Analysis (Actual vs Discounted)

Sheet: Q5_Price_Analysis

Pivot Table Setup:
- Rows: Category
- Values: Average of Actual_Price, Average of Discounted_Price, Average of Discount_Percentage

Key Formulas:
=AVERAGEIF(Data!$B:$B,A2,Data!$R:$R)  Avg actual price
=AVERAGEIF(Data!$B:$B,A2,Data!$S:$S)  Avg discounted price
=B2-C2                                  Average savings

Chart: Clustered column chart comparing actual vs discounted prices

Question 6: Products with Highest Review Counts

Sheet: Q6_High_Review_Products

Analysis Setup:
Top 20 by review count
=LARGE(Data!$I:$I,ROW(A1:A20))
=INDEX(Data!$A:$A,MATCH(A2,Data!$I:$I,0))  Product name
=INDEX(Data!$B:$B,MATCH(A2,Data!$I:$I,0))  Category

Chart: Horizontal bar chart of top 20 products

Question 7: High-Discount Products (50%+)

Sheet: Q7_High_Discount_Analysis

Analysis Setup:
Count products with 50%+ discount
=COUNTIF(Data!$Q:$Q,">=50")

Filter high discount products
=FILTER(Data!A:U,Data!Q:Q>=50)

Analysis by category
=COUNTIFS(Data!$B:$B,A2,Data!$Q:$Q,">=50")

Chart: Bar chart showing high-discount products by category

Question 8: Rating Distribution Analysis

Sheet: Q8_Rating_Distribution

Analysis Setup:
Rating frequency
=COUNTIF(Data!$H:$H,">=4.5")  Excellent
=COUNTIF(Data!$H:$H,">=4")-COUNTIF(Data!$H:$H,">=4.5")  Very Good
Continue for all ranges

Statistical measures
=AVERAGE(Data!$H:$H)  Mean
=MEDIAN(Data!$H:$H)   Median
=MODE(Data!$H:$H)     Mode
=STDEV(Data!$H:$H)    Standard deviation

Charts:
- Histogram of rating distribution
- Pie chart of rating categories

Question 9: Revenue Potential Analysis

Sheet: Q9_Revenue_Potential

Pivot Table Setup:
- Rows: Category
- Values: Sum of Revenue_Potential, Average of Revenue_Potential, Count of Product_Name

Key Formulas:
=SUMIF(Data!$B:$B,A2,Data!$U:$U)      Total revenue potential
=AVERAGEIF(Data!$B:$B,A2,Data!$U:$U)  Average revenue potential

Chart: Waterfall chart showing revenue potential by category

Question 10: Price Range Segmentation

Sheet: Q10_Price_Segmentation

Pivot Table Setup:
- Rows: Price_Range
- Values: Count of Product_Name, Average of Rating, Average of Discount_Percentage

Key Formulas:
=COUNTIF(Data!$R:$R,"<₹200")
=COUNTIF(Data!$R:$R,"₹200-₹500")
=COUNTIF(Data!$R:$R,">₹500")

Chart: Stacked column chart showing distribution and metrics

Question 11: Discount-Rating Correlation

Sheet: Q11_Correlation_Analysis

Analysis Setup:
Correlation coefficient
=CORREL(Data!$Q:$Q,Data!$H:$H)

Regression analysis
=SLOPE(Data!$H:$H,Data!$Q:$Q)   Slope
=INTERCEPT(Data!$H:$H,Data!$Q:$Q)  Intercept
=RSQ(Data!$H:$H,Data!$Q:$Q)     R-squared

Chart: Scatter plot with trendline

Question 12: Low-Review Products

Sheet: Q12_Low_Review_Analysis

Analysis Setup:
Products with <1000 reviews
=COUNTIF(Data!$I:$I,"<1000")

Filter and analyze
=FILTER(Data!A:U,Data!I:I<1000)

Category breakdown
=COUNTIFS(Data!$B:$B,A2,Data!$I:$I,"<1000")

Question 13: Category Discount Leaders

Sheet: Q13_Discount_Leaders

Analysis Setup:
Highest discount by category
=MAXIFS(Data!$Q:$Q,Data!$B:$B,A2)

Product with highest discount in each category
=INDEX(Data!$A:$A,MATCH(1,(Data!$B:$B=A2)*(Data!$Q:$Q=B2),0))

Question 14: Top Combined Metrics

Sheet: Q14_Combined_Analysis

Analysis Setup:
Combined score (Rating * Review_Count * Discount)
=Data!H2*Data!I2*Data!Q2

Weighted scoring
=(Data!H2*0.4)+(Data!I2/MAX(Data!I:I)*0.3)+(Data!Q2/MAX(Data!Q:Q)*0.3)

EXECUTIVE DASHBOARD SHEET

Key Performance Indicators
Total products analyzed
=COUNTA(Data!A:A)-1

Average rating across all products
=AVERAGE(Data!H:H)

Total revenue potential
=SUM(Data!U:U)

Average discount percentage
=AVERAGE(Data!Q:Q)

Top performing category
=INDEX(Q1_Analysis!A:A,MATCH(MAX(Q1_Analysis!B:B),Q1_Analysis!B:B,0))

Dashboard Charts
1. Category Performance Summary - Clustered column chart
2. Rating vs Review Count - Bubble chart
3. Price vs Discount Analysis - Scatter plot
4. Revenue Potential by Category - Pie chart
5. Top 10 Products - Horizontal bar chart

Interactive Elements
- Slicers for Category, Price Range, Rating Category
- Data validation dropdowns for filtering
- Conditional formatting for highlighting insights

BUSINESS INSIGHTS TEMPLATE

For each analysis sheet, include this insights section:

Key Findings:
- [Specific numerical findings]
- [Trends and patterns observed]
- [Outliers or anomalies]

Business Implications:
- [What this means for the business]
- [Opportunities identified]
- [Risks or concerns]

Recommendations:
- [Specific actionable recommendations]
- [Priority level (High/Medium/Low)]
- [Expected impact]

IMPLEMENTATION CHECKLIST

Data Preparation
- [ ] Import Amazon dataset
- [ ] Create calculated columns
- [ ] Validate data quality
- [ ] Set up named ranges

Analysis Sheets (Q1-Q14)
- [ ] Create pivot tables for each question
- [ ] Add supporting formulas
- [ ] Generate appropriate charts
- [ ] Document insights and recommendations

Dashboard Creation
- [ ] Build executive summary dashboard
- [ ] Add interactive elements
- [ ] Create KPI section
- [ ] Include key visualizations

Final Review
- [ ] Verify all formulas work correctly
- [ ] Check chart formatting and clarity
- [ ] Ensure insights are actionable
- [ ] Proofread all text and labels

EXCEL TIPS FOR SUCCESS

1. Use Named Ranges for easier formula management
2. Apply Conditional Formatting to highlight key insights
3. Create Data Validation for interactive elements
4. Use XLOOKUP/INDEX-MATCH for robust lookups
5. Format Numbers Appropriately (currency, percentages)
6. Add Data Labels to charts for clarity
7. Use Consistent Color Schemes across all visualizations
8. Include Source References for all calculations

This comprehensive Excel analysis will provide all the insights needed for your Amazon case study while demonstrating advanced Excel skills for your capstone project.
