{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Amazon Product Review Analysis - Advanced Analytics\n", "## DSA Capstone Project - Case Study 1 (Part 2)\n", "\n", "### Continuing Business Questions Analysis\n", "This notebook covers analyses 6-14 and creates comprehensive insights for business recommendations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries and load data\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from scipy import stats\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Load cleaned dataset\n", "df = pd.read_csv('../data/processed/amazon_products_cleaned.csv')\n", "print(f\"Dataset loaded: {df.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analysis 6: Products with Highest Review Counts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analysis 6: Highest review counts\n", "top_reviewed = df.nlargest(20, 'rating_count')[['product_name', 'category', 'rating', 'rating_count', 'actual_price', 'discount_percentage']]\n", "\n", "print(\"=== TOP 20 PRODUCTS BY REVIEW COUNT ===\")\n", "display(top_reviewed)\n", "\n", "# Review count distribution\n", "plt.figure(figsize=(15, 10))\n", "\n", "plt.subplot(2, 2, 1)\n", "plt.hist(df['rating_count'], bins=50, edgecolor='black')\n", "plt.title('Distribution of Review Counts')\n", "plt.xlabel('Number of Reviews')\n", "plt.ylabel('Number of Products')\n", "\n", "plt.subplot(2, 2, 2)\n", "plt.hist(np.log1p(df['rating_count']), bins=50, edgecolor='black')\n", "plt.title('Log Distribution of Review Counts')\n", "plt.xlabel('Log(Number of Reviews + 1)')\n", "plt.ylabel('Number of Products')\n", "\n", "plt.subplot(2, 2, 3)\n", "review_by_category = df.groupby('category')['rating_count'].mean().sort_values(ascending=False)\n", "sns.barplot(x=review_by_category.values, y=review_by_category.index)\n", "plt.title('Average Reviews by Category')\n", "plt.xlabel('Average Number of Reviews')\n", "\n", "plt.subplot(2, 2, 4)\n", "plt.scatter(df['rating'], df['rating_count'], alpha=0.6)\n", "plt.title('Rating vs Review Count')\n", "plt.xlabel('Product Rating')\n", "plt.ylabel('Number of Reviews')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analysis 7: High-Discount Products (50%+ Discount)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analysis 7: Products with 50%+ discount\n", "high_discount = df[df['discount_percentage'] >= 50].copy()\n", "\n", "print(f\"=== PRODUCTS WITH 50%+ DISCOUNT ===\")\n", "print(f\"Total products with 50%+ discount: {len(high_discount)}\")\n", "print(f\"Percentage of total products: {len(high_discount)/len(df)*100:.2f}%\")\n", "\n", "# High discount analysis by category\n", "high_discount_by_category = high_discount.groupby('category').agg({\n", "    'product_name': 'count',\n", "    'discount_percentage': ['mean', 'max'],\n", "    'rating': 'mean',\n", "    'actual_price': 'mean'\n", "}).round(2)\n", "\n", "high_discount_by_category.columns = ['Count', 'Avg_Discount_%', 'Max_Discount_%', 'Avg_Rating', 'Avg_Price']\n", "high_discount_by_category = high_discount_by_category.sort_values('Count', ascending=False)\n", "\n", "display(high_discount_by_category)\n", "\n", "# Visualization\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 12))\n", "\n", "# High discount products by category\n", "sns.barplot(data=high_discount_by_category.reset_index(), x='Count', y='category', ax=ax1)\n", "ax1.set_title('High Discount Products (50%+) by Category')\n", "ax1.set_xlabel('Number of Products')\n", "\n", "# Discount distribution\n", "ax2.hist(high_discount['discount_percentage'], bins=20, edgecolor='black')\n", "ax2.set_title('Distribution of High Discounts (50%+)')\n", "ax2.set_xlabel('Discount Percentage')\n", "ax2.set_ylabel('Number of Products')\n", "\n", "# Rating vs Discount for high discount products\n", "ax3.scatter(high_discount['discount_percentage'], high_discount['rating'], alpha=0.6)\n", "ax3.set_title('Rating vs Discount (50%+ Discount Products)')\n", "ax3.set_xlabel('Discount Percentage')\n", "ax3.set_ylabel('Rating')\n", "\n", "# Price vs Discount\n", "ax4.scatter(high_discount['actual_price'], high_discount['discount_percentage'], alpha=0.6)\n", "ax4.set_title('Price vs Discount (50%+ Discount Products)')\n", "ax4.set_xlabel('Actual Price (₹)')\n", "ax4.set_ylabel('Discount Percentage')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analysis 8: Rating Distribution Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analysis 8: Detailed rating distribution\n", "rating_dist = df['rating'].value_counts().sort_index()\n", "rating_dist_pct = (rating_dist / len(df) * 100).round(2)\n", "\n", "rating_analysis = pd.DataFrame({\n", "    'Rating': rating_dist.index,\n", "    'Count': rating_dist.values,\n", "    'Percentage': rating_dist_pct.values\n", "})\n", "\n", "print(\"=== RATING DISTRIBUTION ANALYSIS ===\")\n", "display(rating_analysis)\n", "\n", "# Statistical summary of ratings\n", "print(\"\\n=== RATING STATISTICS ===\")\n", "print(f\"Mean Rating: {df['rating'].mean():.2f}\")\n", "print(f\"Median Rating: {df['rating'].median():.2f}\")\n", "print(f\"Mode Rating: {df['rating'].mode().iloc[0]:.1f}\")\n", "print(f\"Standard Deviation: {df['rating'].std():.2f}\")\n", "print(f\"Skewness: {stats.skew(df['rating']):.2f}\")\n", "print(f\"Kurtosis: {stats.kurtosis(df['rating']):.2f}\")\n", "\n", "# Rating ranges\n", "rating_ranges = {\n", "    'Excellent (4.5-5.0)': len(df[df['rating'] >= 4.5]),\n", "    'Very Good (4.0-4.4)': len(df[(df['rating'] >= 4.0) & (df['rating'] < 4.5)]),\n", "    'Good (3.5-3.9)': len(df[(df['rating'] >= 3.5) & (df['rating'] < 4.0)]),\n", "    'Average (3.0-3.4)': len(df[(df['rating'] >= 3.0) & (df['rating'] < 3.5)]),\n", "    'Below Average (<3.0)': len(df[df['rating'] < 3.0])\n", "}\n", "\n", "print(\"\\n=== RATING RANGES ===\")\n", "for range_name, count in rating_ranges.items():\n", "    percentage = (count / len(df)) * 100\n", "    print(f\"{range_name}: {count} products ({percentage:.1f}%)\")\n", "\n", "# Visualization\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# Rating distribution histogram\n", "ax1.hist(df['rating'], bins=30, edgecolor='black', alpha=0.7)\n", "ax1.axvline(df['rating'].mean(), color='red', linestyle='--', label=f'Mean: {df[\"rating\"].mean():.2f}')\n", "ax1.axvline(df['rating'].median(), color='green', linestyle='--', label=f'Median: {df[\"rating\"].median():.2f}')\n", "ax1.set_title('Distribution of Product Ratings')\n", "ax1.set_xlabel('Rating')\n", "ax1.set_ylabel('Number of Products')\n", "ax1.legend()\n", "\n", "# Rating ranges pie chart\n", "ax2.pie(rating_ranges.values(), labels=rating_ranges.keys(), autopct='%1.1f%%', startangle=90)\n", "ax2.set_title('Product Distribution by Rating Ranges')\n", "\n", "# Box plot of ratings by category\n", "sns.boxplot(data=df, x='category', y='rating', ax=ax3)\n", "ax3.set_title('Rating Distribution by Category')\n", "ax3.tick_params(axis='x', rotation=45)\n", "\n", "# Rating vs Review Count\n", "ax4.scatter(df['rating'], df['rating_count'], alpha=0.6)\n", "ax4.set_title('Rating vs Number of Reviews')\n", "ax4.set_xlabel('Product Rating')\n", "ax4.set_ylabel('Number of Reviews')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analysis 9: Revenue Potential Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analysis 9: Revenue potential (actual_price × rating_count)\n", "df['revenue_potential'] = df['actual_price'] * df['rating_count']\n", "\n", "revenue_by_category = df.groupby('category').agg({\n", "    'revenue_potential': ['sum', 'mean', 'median'],\n", "    'actual_price': 'mean',\n", "    'rating_count': 'mean',\n", "    'product_name': 'count'\n", "}).round(2)\n", "\n", "revenue_by_category.columns = ['Total_Revenue_Potential', 'Avg_Revenue_Potential', 'Median_Revenue_Potential', \n", "                              'Avg_Price', 'Avg_Reviews', 'Product_Count']\n", "revenue_by_category = revenue_by_category.sort_values('Total_Revenue_Potential', ascending=False)\n", "\n", "print(\"=== REVENUE POTENTIAL BY CATEGORY ===\")\n", "display(revenue_by_category)\n", "\n", "# Top revenue potential products\n", "top_revenue_products = df.nlargest(15, 'revenue_potential')[['product_name', 'category', 'actual_price', 'rating_count', 'revenue_potential', 'rating']]\n", "\n", "print(\"\\n=== TOP 15 PRODUCTS BY REVENUE POTENTIAL ===\")\n", "display(top_revenue_products)\n", "\n", "# Visualization\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 12))\n", "\n", "# Total revenue potential by category\n", "sns.barplot(data=revenue_by_category.reset_index(), x='Total_Revenue_Potential', y='category', ax=ax1)\n", "ax1.set_title('Total Revenue Potential by Category')\n", "ax1.set_xlabel('Total Revenue Potential (₹)')\n", "\n", "# Average revenue potential by category\n", "sns.barplot(data=revenue_by_category.reset_index(), x='Avg_Revenue_Potential', y='category', ax=ax2)\n", "ax2.set_title('Average Revenue Potential by Category')\n", "ax2.set_xlabel('Average Revenue Potential (₹)')\n", "\n", "# Revenue potential distribution\n", "ax3.hist(np.log1p(df['revenue_potential']), bins=50, edgecolor='black')\n", "ax3.set_title('Distribution of Revenue Potential (Log Scale)')\n", "ax3.set_xlabel('Log(Revenue Potential + 1)')\n", "ax3.set_ylabel('Number of Products')\n", "\n", "# Price vs Reviews (bubble chart with revenue potential)\n", "scatter = ax4.scatter(df['actual_price'], df['rating_count'], \n", "                     s=df['revenue_potential']/df['revenue_potential'].max()*100, \n", "                     alpha=0.6, c=df['rating'], cmap='viridis')\n", "ax4.set_title('Price vs Reviews (Bubble size = Revenue Potential)')\n", "ax4.set_xlabel('Actual Price (₹)')\n", "ax4.set_ylabel('Number of Reviews')\n", "plt.colorbar(scatter, ax=ax4, label='Rating')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}