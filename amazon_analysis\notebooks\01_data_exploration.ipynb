{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Amazon Product Review Analysis - Data Exploration\n", "## DSA Capstone Project - Case Study 1\n", "\n", "### Objective\n", "Initial exploration of Amazon product review dataset to understand structure, quality, and characteristics.\n", "\n", "### Dataset Overview\n", "- **Records**: 1,465 rows\n", "- **Fields**: 16 columns\n", "- **Source**: Amazon product pages (scraped data)\n", "- **Content**: Product details, customer reviews, ratings, pricing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_rows', 100)\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. Data Loading and Initial Inspection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the dataset\n", "# Note: Update the file path when actual dataset is available\n", "try:\n", "    df = pd.read_csv('../data/raw/amazon_products.csv')\n", "    print(f\"Dataset loaded successfully!\")\n", "    print(f\"Shape: {df.shape}\")\nexcept FileNotFoundError:\n", "    print(\"Dataset file not found. Please ensure the data file is in the correct location.\")\n", "    print(\"Expected location: ../data/raw/amazon_products.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display basic information about the dataset\n", "if 'df' in locals():\n", "    print(\"=== DATASET OVERVIEW ===\")\n", "    print(f\"Rows: {df.shape[0]}\")\n", "    print(f\"Columns: {df.shape[1]}\")\n", "    print(\"\\n=== COLUMN INFORMATION ===\")\n", "    print(df.info())\n", "    print(\"\\n=== FIRST FEW ROWS ===\")\n", "    display(df.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. Data Quality Assessment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for missing values\n", "if 'df' in locals():\n", "    print(\"=== MISSING VALUES ANALYSIS ===\")\n", "    missing_data = df.isnull().sum()\n", "    missing_percent = (missing_data / len(df)) * 100\n", "    \n", "    missing_df = pd.DataFrame({\n", "        'Column': missing_data.index,\n", "        'Missing_Count': missing_data.values,\n", "        'Missing_Percentage': missing_percent.values\n", "    })\n", "    \n", "    missing_df = missing_df[missing_df['Missing_Count'] > 0].sort_values('Missing_Count', ascending=False)\n", "    \n", "    if len(missing_df) > 0:\n", "        display(missing_df)\n", "    else:\n", "        print(\"No missing values found!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for duplicates\n", "if 'df' in locals():\n", "    print(\"=== DUPLICATE ANALYSIS ===\")\n", "    duplicates = df.duplicated().sum()\n", "    print(f\"Total duplicate rows: {duplicates}\")\n", "    \n", "    if duplicates > 0:\n", "        print(f\"Percentage of duplicates: {(duplicates/len(df))*100:.2f}%\")\n", "    else:\n", "        print(\"No duplicate rows found!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. Data Types and Structure Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze data types\n", "if 'df' in locals():\n", "    print(\"=== DATA TYPES ANALYSIS ===\")\n", "    dtype_df = pd.DataFrame({\n", "        'Column': df.columns,\n", "        'Data_Type': df.dtypes.values,\n", "        'Non_Null_Count': df.count().values,\n", "        'Unique_Values': [df[col].nunique() for col in df.columns]\n", "    })\n", "    \n", "    display(dtype_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. Statistical Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate statistical summary for numerical columns\n", "if 'df' in locals():\n", "    print(\"=== NUMERICAL COLUMNS SUMMARY ===\")\n", "    numerical_cols = df.select_dtypes(include=[np.number]).columns\n", "    \n", "    if len(numerical_cols) > 0:\n", "        display(df[numerical_cols].describe())\n", "    else:\n", "        print(\"No numerical columns found.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate summary for categorical columns\n", "if 'df' in locals():\n", "    print(\"=== CATEGORICAL COLUMNS SUMMARY ===\")\n", "    categorical_cols = df.select_dtypes(include=['object']).columns\n", "    \n", "    for col in categorical_cols[:5]:  # Show first 5 categorical columns\n", "        print(f\"\\n--- {col} ---\")\n", "        print(f\"Unique values: {df[col].nunique()}\")\n", "        print(\"Top 5 values:\")\n", "        print(df[col].value_counts().head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5. Initial Insights and Observations\n", "\n", "**Key Findings from Data Exploration:**\n", "\n", "*[To be filled after running the analysis with actual data]*\n", "\n", "1. **Dataset Structure**: \n", "2. **Data Quality**: \n", "3. **Key Variables**: \n", "4. **Potential Issues**: \n", "5. **Next Steps**: \n", "\n", "### Next Steps\n", "1. Data cleaning and preprocessing (Notebook 02)\n", "2. Detailed EDA analysis (Notebook 03)\n", "3. Insights and visualization (Notebook 04)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}