{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Amazon Product Review Analysis - Comprehensive EDA\n", "## DSA Capstone Project - Case Study 1\n", "\n", "### Objective\n", "Perform comprehensive Exploratory Data Analysis to answer all 14 business questions and generate actionable insights for RetailTech Insights.\n", "\n", "### Business Questions to Answer\n", "1. Average discount percentage by product category\n", "2. Product distribution across categories\n", "3. Total reviews per category\n", "4. Highest-rated products identification\n", "5. Price analysis (actual vs discounted) by category\n", "6. Products with highest review counts\n", "7. High-discount products (50%+ discount)\n", "8. Rating distribution analysis\n", "9. Revenue potential analysis\n", "10. Price range segmentation\n", "11. Discount-rating correlation\n", "12. Low-review products identification\n", "13. Category-wise discount leaders\n", "14. Top products by combined rating and review metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.float_format', '{:.2f}'.format)\n", "\n", "# Set plotting parameters\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "sns.set_style(\"whitegrid\")\n", "sns.set_palette(\"Set2\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load cleaned dataset\n", "try:\n", "    df = pd.read_csv('../data/processed/amazon_products_cleaned.csv')\n", "    print(f\"Cleaned dataset loaded successfully!\")\n", "    print(f\"Shape: {df.shape}\")\n", "    print(f\"Columns: {list(df.columns)}\")\nexcept FileNotFoundError:\n", "    print(\"Cleaned dataset not found. Please run data cleaning notebook first.\")\n", "    print(\"Expected location: ../data/processed/amazon_products_cleaned.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analysis 1: Average Discount Percentage by Product Category"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate discount percentage if not already present\n", "if 'df' in locals():\n", "    # Assuming columns: actual_price, discounted_price, category\n", "    if 'discount_percentage' not in df.columns:\n", "        df['discount_percentage'] = ((df['actual_price'] - df['discounted_price']) / df['actual_price']) * 100\n", "    \n", "    # Analysis 1: Average discount by category\n", "    discount_by_category = df.groupby('category').agg({\n", "        'discount_percentage': ['mean', 'median', 'std', 'count'],\n", "        'actual_price': 'mean',\n", "        'discounted_price': 'mean'\n", "    }).round(2)\n", "    \n", "    discount_by_category.columns = ['Avg_Discount_%', 'Median_Discount_%', 'Std_Discount_%', 'Product_Count', 'Avg_Actual_Price', 'Avg_Discounted_Price']\n", "    discount_by_category = discount_by_category.sort_values('Avg_Discount_%', ascending=False)\n", "    \n", "    print(\"=== AVERAGE DISCOUNT PERCENTAGE BY CATEGORY ===\")\n", "    display(discount_by_category)\n", "    \n", "    # Visualization\n", "    plt.figure(figsize=(14, 8))\n", "    sns.barplot(data=df, x='category', y='discount_percentage', ci=95)\n", "    plt.title('Average Discount Percentage by Product Category', fontsize=16, fontweight='bold')\n", "    plt.xlabel('Product Category', fontsize=12)\n", "    plt.ylabel('Discount Percentage (%)', fontsize=12)\n", "    plt.xticks(rotation=45, ha='right')\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analysis 2: Product Distribution Across Categories"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'df' in locals():\n", "    # Analysis 2: Product count by category\n", "    category_distribution = df['category'].value_counts().reset_index()\n", "    category_distribution.columns = ['Category', 'Product_Count']\n", "    category_distribution['Percentage'] = (category_distribution['Product_Count'] / len(df) * 100).round(2)\n", "    \n", "    print(\"=== PRODUCT DISTRIBUTION BY CATEGORY ===\")\n", "    display(category_distribution)\n", "    \n", "    # Visualization - Pie Chart\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))\n", "    \n", "    # Pie chart\n", "    ax1.pie(category_distribution['Product_Count'], labels=category_distribution['Category'], \n", "            autopct='%1.1f%%', startangle=90)\n", "    ax1.set_title('Product Distribution by Category', fontsize=14, fontweight='bold')\n", "    \n", "    # Bar chart\n", "    sns.barplot(data=category_distribution, x='Product_Count', y='Category', ax=ax2)\n", "    ax2.set_title('Product Count by Category', fontsize=14, fontweight='bold')\n", "    ax2.set_xlabel('Number of Products')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analysis 3: Total Reviews per Category"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'df' in locals():\n", "    # Analysis 3: Total reviews by category\n", "    # Assuming column: rating_count or review_count\n", "    reviews_by_category = df.groupby('category').agg({\n", "        'rating_count': ['sum', 'mean', 'median', 'max'],\n", "        'product_name': 'count'\n", "    }).round(2)\n", "    \n", "    reviews_by_category.columns = ['Total_Reviews', 'Avg_Reviews_Per_Product', 'Median_Reviews', 'Max_Reviews', 'Product_Count']\n", "    reviews_by_category = reviews_by_category.sort_values('Total_Reviews', ascending=False)\n", "    \n", "    print(\"=== TOTAL REVIEWS PER CATEGORY ===\")\n", "    display(reviews_by_category)\n", "    \n", "    # Visualization\n", "    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))\n", "    \n", "    # Total reviews by category\n", "    sns.barplot(data=df.groupby('category')['rating_count'].sum().reset_index(), \n", "                x='category', y='rating_count', ax=ax1)\n", "    ax1.set_title('Total Reviews by Category', fontsize=14, fontweight='bold')\n", "    ax1.set_xlabel('Category')\n", "    ax1.set_ylabel('Total Reviews')\n", "    ax1.tick_params(axis='x', rotation=45)\n", "    \n", "    # Average reviews per product by category\n", "    sns.barplot(data=df, x='category', y='rating_count', ci=95, ax=ax2)\n", "    ax2.set_title('Average Reviews per Product by Category', fontsize=14, fontweight='bold')\n", "    ax2.set_xlabel('Category')\n", "    ax2.set_ylabel('Average Reviews per Product')\n", "    ax2.tick_params(axis='x', rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analysis 4: Highest-Rated Products"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'df' in locals():\n", "    # Analysis 4: Highest rated products\n", "    # Filter products with significant number of reviews (e.g., >100) for reliability\n", "    min_reviews = 100\n", "    reliable_products = df[df['rating_count'] >= min_reviews].copy()\n", "    \n", "    # Top 20 highest-rated products\n", "    top_rated = reliable_products.nlargest(20, 'rating')[['product_name', 'category', 'rating', 'rating_count', 'actual_price', 'discount_percentage']]\n", "    \n", "    print(f\"=== TOP 20 HIGHEST-RATED PRODUCTS (min {min_reviews} reviews) ===\")\n", "    display(top_rated)\n", "    \n", "    # Rating distribution analysis\n", "    rating_distribution = df['rating'].value_counts().sort_index()\n", "    \n", "    print(\"\\n=== RATING DISTRIBUTION ===\")\n", "    display(rating_distribution)\n", "    \n", "    # Visualization\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))\n", "    \n", "    # Rating distribution histogram\n", "    df['rating'].hist(bins=20, ax=ax1, edgecolor='black')\n", "    ax1.set_title('Distribution of Product Ratings', fontsize=14, fontweight='bold')\n", "    ax1.set_xlabel('Rating')\n", "    ax1.set_ylabel('Number of Products')\n", "    \n", "    # Average rating by category\n", "    avg_rating_by_category = df.groupby('category')['rating'].mean().sort_values(ascending=False)\n", "    sns.barplot(x=avg_rating_by_category.values, y=avg_rating_by_category.index, ax=ax2)\n", "    ax2.set_title('Average Rating by Category', fontsize=14, fontweight='bold')\n", "    ax2.set_xlabel('Average Rating')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analysis 5: Price Analysis (Actual vs Discounted) by Category"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'df' in locals():\n", "    # Analysis 5: Price comparison by category\n", "    price_analysis = df.groupby('category').agg({\n", "        'actual_price': ['mean', 'median', 'std'],\n", "        'discounted_price': ['mean', 'median', 'std'],\n", "        'discount_percentage': 'mean'\n", "    }).round(2)\n", "    \n", "    price_analysis.columns = ['Avg_Actual_Price', 'Median_Actual_Price', 'Std_Actual_Price',\n", "                             'Avg_Discounted_Price', 'Median_Discounted_Price', 'Std_Discounted_Price',\n", "                             'Avg_Discount_%']\n", "    \n", "    # Calculate savings\n", "    price_analysis['Avg_Savings'] = price_analysis['Avg_Actual_Price'] - price_analysis['Avg_Discounted_Price']\n", "    \n", "    print(\"=== PRICE ANALYSIS BY CATEGORY ===\")\n", "    display(price_analysis)\n", "    \n", "    # Visualization\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))\n", "    \n", "    # Actual vs Discounted Price by Category\n", "    categories = price_analysis.index\n", "    x_pos = np.arange(len(categories))\n", "    \n", "    ax1.bar(x_pos - 0.2, price_analysis['Avg_Actual_Price'], 0.4, label='Actual Price', alpha=0.8)\n", "    ax1.bar(x_pos + 0.2, price_analysis['Avg_Discounted_Price'], 0.4, label='Discounted Price', alpha=0.8)\n", "    ax1.set_title('Average Actual vs Discounted Price by Category', fontsize=14, fontweight='bold')\n", "    ax1.set_xlabel('Category')\n", "    ax1.set_ylabel('Price (₹)')\n", "    ax1.set_xticks(x_pos)\n", "    ax1.set_xticklabels(categories, rotation=45, ha='right')\n", "    ax1.legend()\n", "    \n", "    # Price distribution boxplot\n", "    df.boxplot(column='actual_price', by='category', ax=ax2)\n", "    ax2.set_title('Actual Price Distribution by Category', fontsize=14, fontweight='bold')\n", "    ax2.set_xlabel('Category')\n", "    ax2.set_ylabel('Actual Price (₹)')\n", "    \n", "    # Discount percentage by category\n", "    sns.barplot(data=df, x='category', y='discount_percentage', ax=ax3)\n", "    ax3.set_title('Average Discount Percentage by Category', fontsize=14, fontweight='bold')\n", "    ax3.set_xlabel('Category')\n", "    ax3.set_ylabel('Discount Percentage (%)')\n", "    ax3.tick_params(axis='x', rotation=45)\n", "    \n", "    # Savings by category\n", "    sns.barplot(x=price_analysis['Avg_Savings'], y=price_analysis.index, ax=ax4)\n", "    ax4.set_title('Average Savings by Category', fontsize=14, fontweight='bold')\n", "    ax4.set_xlabel('Average Savings (₹)')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}