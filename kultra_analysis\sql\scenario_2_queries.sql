-- Kultra Mega Stores (KMS) Business Intelligence Analysis
-- Case Scenario II: Customer & Profitability Analysis
-- DSA Capstone Project - Case Study 2

-- =====================================================
-- SCENARIO II ANALYSIS QUERIES
-- =====================================================

-- Question 6: Most valuable customers and their purchase patterns
-- =====================================================
SELECT 
    customer_name,
    customer_id,
    segment,
    SUM(sales) as total_sales,
    SUM(profit) as total_profit,
    COUNT(*) as total_orders,
    AVG(sales) as avg_order_value,
    AVG(profit) as avg_profit_per_order,
    SUM(quantity) as total_quantity,
    COUNT(DISTINCT category) as categories_purchased,
    COUNT(DISTINCT sub_category) as subcategories_purchased,
    MIN(order_date) as first_purchase,
    MAX(order_date) as last_purchase,
    DATEDIFF(MAX(order_date), MIN(order_date)) as customer_lifespan_days
FROM orders
GROUP BY customer_name, customer_id, segment
ORDER BY total_sales DESC
LIMIT 20;

-- Most valuable customers' product preferences
WITH top_customers AS (
    SELECT customer_id
    FROM orders
    GROUP BY customer_id
    ORDER BY SUM(sales) DESC
    LIMIT 10
)
SELECT 
    o.customer_name,
    o.category,
    o.sub_category,
    COUNT(*) as purchase_frequency,
    SUM(o.sales) as category_sales,
    SUM(o.profit) as category_profit,
    AVG(o.sales) as avg_category_order_value
FROM orders o
INNER JOIN top_customers tc ON o.customer_id = tc.customer_id
GROUP BY o.customer_name, o.category, o.sub_category
ORDER BY o.customer_name, category_sales DESC;

-- Customer value segmentation
SELECT 
    CASE 
        WHEN total_sales >= 10000 THEN 'High Value (>$10K)'
        WHEN total_sales >= 5000 THEN 'Medium Value ($5K-$10K)'
        WHEN total_sales >= 1000 THEN 'Low Value ($1K-$5K)'
        ELSE 'Very Low Value (<$1K)'
    END as customer_segment,
    COUNT(*) as customer_count,
    AVG(total_sales) as avg_sales_per_customer,
    SUM(total_sales) as segment_total_sales,
    ROUND(SUM(total_sales) / (SELECT SUM(sales) FROM orders) * 100, 2) as revenue_contribution_pct
FROM (
    SELECT customer_id, SUM(sales) as total_sales
    FROM orders
    GROUP BY customer_id
) customer_totals
GROUP BY customer_segment
ORDER BY avg_sales_per_customer DESC;


-- Question 7: Small business customer with highest sales
-- =====================================================
SELECT 
    customer_name,
    customer_id,
    segment,
    SUM(sales) as total_sales,
    SUM(profit) as total_profit,
    COUNT(*) as total_orders,
    AVG(sales) as avg_order_value,
    SUM(quantity) as total_quantity,
    COUNT(DISTINCT category) as categories_purchased,
    MIN(order_date) as first_order,
    MAX(order_date) as last_order
FROM orders
WHERE LOWER(segment) = 'small business' OR LOWER(segment) LIKE '%small%'
GROUP BY customer_name, customer_id, segment
ORDER BY total_sales DESC
LIMIT 1;

-- Top 10 small business customers
SELECT 
    customer_name,
    customer_id,
    SUM(sales) as total_sales,
    SUM(profit) as total_profit,
    COUNT(*) as total_orders,
    AVG(sales) as avg_order_value,
    ROUND(SUM(profit) / SUM(sales) * 100, 2) as profit_margin_pct
FROM orders
WHERE LOWER(segment) = 'small business' OR LOWER(segment) LIKE '%small%'
GROUP BY customer_name, customer_id
ORDER BY total_sales DESC
LIMIT 10;


-- Question 8: Corporate customer with most orders (2009-2012)
-- =====================================================
SELECT 
    customer_name,
    customer_id,
    segment,
    COUNT(*) as total_orders,
    SUM(sales) as total_sales,
    SUM(profit) as total_profit,
    AVG(sales) as avg_order_value,
    COUNT(DISTINCT YEAR(order_date)) as years_active,
    MIN(order_date) as first_order,
    MAX(order_date) as last_order
FROM orders
WHERE (LOWER(segment) = 'corporate' OR LOWER(segment) LIKE '%corporate%')
    AND YEAR(order_date) BETWEEN 2009 AND 2012
GROUP BY customer_name, customer_id, segment
ORDER BY total_orders DESC
LIMIT 1;

-- Top 10 corporate customers by order frequency
SELECT 
    customer_name,
    customer_id,
    COUNT(*) as total_orders,
    SUM(sales) as total_sales,
    AVG(sales) as avg_order_value,
    COUNT(DISTINCT YEAR(order_date)) as years_active,
    ROUND(COUNT(*) / COUNT(DISTINCT YEAR(order_date)), 1) as avg_orders_per_year
FROM orders
WHERE (LOWER(segment) = 'corporate' OR LOWER(segment) LIKE '%corporate%')
    AND YEAR(order_date) BETWEEN 2009 AND 2012
GROUP BY customer_name, customer_id
ORDER BY total_orders DESC
LIMIT 10;

-- Corporate customer ordering patterns by year
SELECT 
    YEAR(order_date) as year,
    COUNT(DISTINCT customer_id) as unique_corporate_customers,
    COUNT(*) as total_corporate_orders,
    SUM(sales) as total_corporate_sales,
    AVG(sales) as avg_order_value
FROM orders
WHERE (LOWER(segment) = 'corporate' OR LOWER(segment) LIKE '%corporate%')
    AND YEAR(order_date) BETWEEN 2009 AND 2012
GROUP BY YEAR(order_date)
ORDER BY year;


-- Question 9: Most profitable consumer customer
-- =====================================================
SELECT 
    customer_name,
    customer_id,
    segment,
    SUM(profit) as total_profit,
    SUM(sales) as total_sales,
    COUNT(*) as total_orders,
    AVG(profit) as avg_profit_per_order,
    ROUND(SUM(profit) / SUM(sales) * 100, 2) as profit_margin_pct,
    SUM(quantity) as total_quantity,
    MIN(order_date) as first_order,
    MAX(order_date) as last_order
FROM orders
WHERE LOWER(segment) = 'consumer' OR LOWER(segment) LIKE '%consumer%'
GROUP BY customer_name, customer_id, segment
ORDER BY total_profit DESC
LIMIT 1;

-- Top 10 most profitable consumer customers
SELECT 
    customer_name,
    customer_id,
    SUM(profit) as total_profit,
    SUM(sales) as total_sales,
    COUNT(*) as total_orders,
    ROUND(SUM(profit) / SUM(sales) * 100, 2) as profit_margin_pct,
    AVG(profit) as avg_profit_per_order
FROM orders
WHERE LOWER(segment) = 'consumer' OR LOWER(segment) LIKE '%consumer%'
GROUP BY customer_name, customer_id
ORDER BY total_profit DESC
LIMIT 10;

-- Consumer profitability by category
SELECT 
    category,
    COUNT(DISTINCT customer_id) as unique_consumers,
    SUM(profit) as total_profit,
    SUM(sales) as total_sales,
    ROUND(SUM(profit) / SUM(sales) * 100, 2) as profit_margin_pct,
    AVG(profit) as avg_profit_per_order
FROM orders
WHERE LOWER(segment) = 'consumer' OR LOWER(segment) LIKE '%consumer%'
GROUP BY category
ORDER BY total_profit DESC;


-- Question 10: Customers who returned items and their segments
-- =====================================================
-- Note: Assuming negative profit or specific return indicators
SELECT 
    customer_name,
    customer_id,
    segment,
    COUNT(*) as return_incidents,
    SUM(sales) as total_return_sales,
    SUM(profit) as total_return_impact,
    AVG(profit) as avg_return_impact,
    MIN(order_date) as first_return,
    MAX(order_date) as last_return
FROM orders
WHERE profit < 0  -- Assuming negative profit indicates returns
GROUP BY customer_name, customer_id, segment
ORDER BY return_incidents DESC;

-- Return analysis by segment
SELECT 
    segment,
    COUNT(DISTINCT customer_id) as customers_with_returns,
    COUNT(*) as total_return_incidents,
    SUM(sales) as total_return_sales,
    SUM(profit) as total_return_impact,
    AVG(profit) as avg_return_impact_per_incident,
    ROUND(COUNT(*) / (SELECT COUNT(*) FROM orders WHERE profit < 0) * 100, 2) as pct_of_all_returns
FROM orders
WHERE profit < 0
GROUP BY segment
ORDER BY total_return_incidents DESC;

-- Return analysis by category
SELECT 
    category,
    sub_category,
    COUNT(*) as return_incidents,
    COUNT(DISTINCT customer_id) as customers_affected,
    SUM(sales) as total_return_sales,
    SUM(profit) as total_return_impact,
    ROUND(COUNT(*) / (SELECT COUNT(*) FROM orders WHERE profit < 0) * 100, 2) as pct_of_all_returns
FROM orders
WHERE profit < 0
GROUP BY category, sub_category
ORDER BY return_incidents DESC;


-- Question 11: Shipping cost optimization analysis
-- =====================================================
-- Shipping method efficiency by order priority
SELECT 
    ship_mode,
    order_priority,
    COUNT(*) as order_count,
    AVG(shipping_cost) as avg_shipping_cost,
    AVG(DATEDIFF(ship_date, order_date)) as avg_delivery_days,
    SUM(shipping_cost) as total_shipping_cost,
    AVG(sales) as avg_order_value,
    ROUND(AVG(shipping_cost) / AVG(sales) * 100, 2) as shipping_cost_pct_of_sales
FROM orders
WHERE ship_date IS NOT NULL AND order_date IS NOT NULL
GROUP BY ship_mode, order_priority
ORDER BY ship_mode, order_priority;

-- Shipping cost vs order priority analysis
SELECT 
    order_priority,
    COUNT(*) as total_orders,
    AVG(shipping_cost) as avg_shipping_cost,
    SUM(shipping_cost) as total_shipping_cost,
    AVG(DATEDIFF(ship_date, order_date)) as avg_delivery_days,
    COUNT(CASE WHEN ship_mode = 'Delivery Truck' THEN 1 END) as delivery_truck_count,
    COUNT(CASE WHEN ship_mode = 'Express Air' THEN 1 END) as express_air_count,
    ROUND(AVG(CASE WHEN ship_mode = 'Delivery Truck' THEN shipping_cost END), 2) as avg_truck_cost,
    ROUND(AVG(CASE WHEN ship_mode = 'Express Air' THEN shipping_cost END), 2) as avg_air_cost
FROM orders
WHERE ship_date IS NOT NULL AND order_date IS NOT NULL
GROUP BY order_priority
ORDER BY 
    CASE order_priority
        WHEN 'Critical' THEN 1
        WHEN 'High' THEN 2
        WHEN 'Medium' THEN 3
        WHEN 'Low' THEN 4
    END;

-- Shipping optimization recommendations
SELECT 
    order_priority,
    ship_mode,
    COUNT(*) as order_count,
    AVG(shipping_cost) as avg_shipping_cost,
    AVG(DATEDIFF(ship_date, order_date)) as avg_delivery_days,
    CASE 
        WHEN order_priority IN ('Critical', 'High') AND ship_mode != 'Express Air' THEN 'Consider Express Air'
        WHEN order_priority IN ('Low', 'Medium') AND ship_mode = 'Express Air' THEN 'Consider Delivery Truck'
        ELSE 'Appropriate shipping method'
    END as recommendation
FROM orders
WHERE ship_date IS NOT NULL AND order_date IS NOT NULL
GROUP BY order_priority, ship_mode
ORDER BY order_priority, avg_shipping_cost DESC;
