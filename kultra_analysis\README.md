# Kultra Mega Stores Inventory Analysis
## DSA Data Analysis Capstone Project - Case Study 2

### Project Overview
This repository contains a comprehensive Business Intelligence analysis for Kultra Mega Stores (KMS), specializing in office supplies and furniture. The analysis focuses on order data from 2009-2012 to provide strategic insights for the Abuja division.

### Business Context
**Company**: Kultra Mega Stores (KMS)  
**Location**: Lagos, Nigeria (analyzing Abuja division)  
**Role**: Business Intelligence Analyst  
**Objective**: Analyze order data to identify trends, optimize operations, and increase revenue

### Dataset Description
- **Source**: KMS order data (2009-2012)
- **Business Types**: Individual consumers, small businesses (retail), large corporate clients (wholesale)
- **Geographic Scope**: Lagos, Nigeria
- **Data Format**: Excel file with order transactions

### Analysis Scenarios

#### Case Scenario I - Sales & Operations Analysis
1. Product category performance analysis
2. Regional sales performance (Top 3 & Bottom 3)
3. Appliances sales in Ontario
4. Revenue optimization strategies for bottom 10 customers
5. Shipping cost analysis by method

#### Case Scenario II - Customer & Profitability Analysis
6. Most valuable customers identification
7. Top small business customer analysis
8. Corporate customer order frequency (2009-2012)
9. Most profitable consumer customer
10. Product return analysis by customer segment
11. Shipping cost optimization based on order priority

### Technical Approach
- **Primary Tool**: SQL for data analysis
- **Secondary Tools**: Excel for visualization and reporting
- **Analysis Method**: Business Intelligence techniques

### Deliverables
- **SQL Scripts**: Comprehensive queries for all analysis scenarios
- **Data Insights**: Detailed findings for each business question
- **Visualizations**: Charts and graphs supporting key findings
- **Business Recommendations**: Strategic advice for management
- **Executive Dashboard**: Summary of key metrics and insights

### Tools & Technologies
- **SQL**: Primary analysis tool (MySQL/PostgreSQL/SQLite)
- **Excel**: Data visualization and dashboard creation
- **Python**: Additional analysis and automation (if needed)
- **Git**: Version control

### Repository Structure
```
kultra_analysis/
├── data/
│   ├── raw/                    # Original Excel files
│   ├── processed/              # Cleaned datasets
│   └── sql_exports/            # Query results
├── sql/
│   ├── data_exploration.sql
│   ├── scenario_1_queries.sql
│   ├── scenario_2_queries.sql
│   └── summary_analysis.sql
├── notebooks/
│   ├── 01_data_import.ipynb
│   ├── 02_sql_analysis.ipynb
│   └── 03_visualization.ipynb
├── dashboards/
│   └── kms_business_dashboard.xlsx
├── reports/
│   ├── scenario_1_analysis.md
│   ├── scenario_2_analysis.md
│   └── executive_summary.md
├── requirements.txt
└── README.md
```

### Key Business Questions

#### Scenario I Questions:
1. Which product category had the highest sales?
2. What are the Top 3 and Bottom 3 regions in terms of sales?
3. What were the total sales of appliances in Ontario?
4. How to increase revenue from bottom 10 customers?
5. Which shipping method incurred the most shipping cost?

#### Scenario II Questions:
6. Who are the most valuable customers and their purchase patterns?
7. Which small business customer had the highest sales?
8. Which corporate customer placed the most orders (2009-2012)?
9. Which consumer customer was the most profitable?
10. Which customers returned items and their segments?
11. Shipping cost optimization analysis based on order priority

### Getting Started
1. Clone this repository
2. Set up SQL environment (MySQL/PostgreSQL recommended)
3. Import data from Excel files to database
4. Run SQL scripts in sequence
5. Review analysis results and visualizations

### Expected Outcomes
- Identification of top-performing product categories
- Regional performance insights
- Customer segmentation and value analysis
- Shipping cost optimization recommendations
- Revenue enhancement strategies

### Key Findings
*[To be updated after analysis completion]*

### Business Recommendations
*[To be updated after analysis completion]*

### Author
DSA Data Analysis Capstone Project  
*[Your Name]*  
*[Date]*
